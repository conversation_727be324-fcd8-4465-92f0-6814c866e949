﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class FocrateCard
{
    public int Id { get; set; }

    public string Title { get; set; } = null!;

    public DateTime DateFrom { get; set; }

    public DateTime DateTo { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public string? ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int Priority { get; set; }

    public bool IsActive { get; set; }

    public bool AllEditions { get; set; }

    public bool AllGovCategories { get; set; }

    public bool AllGovAdType { get; set; }

    public decimal DiscountPer { get; set; }

    public bool AllClients { get; set; }

    public bool AllIndustries { get; set; }

    public bool AllCities { get; set; }

    public bool IsDeleted { get; set; }

    public virtual ICollection<FactAdvertising> FactAdvertisings { get; set; } = new List<FactAdvertising>();

    public virtual ICollection<FocrateCardAdType> FocrateCardAdTypes { get; set; } = new List<FocrateCardAdType>();

    public virtual ICollection<FocrateCardCategory> FocrateCardCategories { get; set; } = new List<FocrateCardCategory>();

    public virtual ICollection<FocrateCardCity> FocrateCardCities { get; set; } = new List<FocrateCardCity>();

    public virtual ICollection<FocrateCardClient> FocrateCardClients { get; set; } = new List<FocrateCardClient>();

    public virtual ICollection<FocrateCardEdition> FocrateCardEditions { get; set; } = new List<FocrateCardEdition>();

    public virtual ICollection<FocrateCardGovAdType> FocrateCardGovAdTypes { get; set; } = new List<FocrateCardGovAdType>();

    public virtual ICollection<FocrateCardGovCategory> FocrateCardGovCategories { get; set; } = new List<FocrateCardGovCategory>();

    public virtual ICollection<FocrateCardIndustry> FocrateCardIndustries { get; set; } = new List<FocrateCardIndustry>();

    public virtual ICollection<FocrateCardSubPublication> FocrateCardSubPublications { get; set; } = new List<FocrateCardSubPublication>();
}