﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class DimCity
{
    public int CityId { get; set; }

    public string CityName { get; set; } = null!;

    public int? CountryId { get; set; }

    public virtual DimCountry? Country { get; set; }

    public virtual ICollection<FactAdvertising> FactAdvertisings { get; set; } = new List<FactAdvertising>();

    public virtual ICollection<FocrateCardCity> FocrateCardCities { get; set; } = new List<FocrateCardCity>();

    public virtual ICollection<RateCardCity> RateCardCities { get; set; } = new List<RateCardCity>();
}