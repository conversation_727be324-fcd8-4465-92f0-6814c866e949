﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class DimGovAdType
{
    public int GovAdTypeId { get; set; }

    public string GovAdTypeName { get; set; } = null!;

    public virtual ICollection<FactAdvertising> FactAdvertisings { get; set; } = new List<FactAdvertising>();

    public virtual ICollection<FocrateCardAdType> FocrateCardAdTypes { get; set; } = new List<FocrateCardAdType>();

    public virtual ICollection<FocrateCardGovAdType> FocrateCardGovAdTypes { get; set; } = new List<FocrateCardGovAdType>();

    public virtual ICollection<RateCardGovAdType> RateCardGovAdTypes { get; set; } = new List<RateCardGovAdType>();
}