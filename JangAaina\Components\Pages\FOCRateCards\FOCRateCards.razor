@page "/setup/focratecards"
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@inject FOCRateCardService service
@rendermode InteractiveServer
@inject IJSRuntime js
@attribute [Authorize]
<style>
    /* FOC Rate Card Table Styles */
    .FOCRateCardTable {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1rem;
        font-size: 0.7rem;
    }

    .FOCRateCardTable th,
    .FOCRateCardTable td {
        padding: 0.45rem;
        vertical-align: top;
        border-top: 1px solid #dee2e6;
        text-align: left;
    }

    .FOCRateCardTable thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
        background-color: #f8f9fa;
        font-weight: 500;
        white-space: nowrap;
    }

    .FOCRateCardTable tbody + tbody {
        border-top: 2px solid #dee2e6;
    }

    .FOCRateCardTable.table-bordered {
        border: 1px solid #dee2e6;
    }

    .FOCRateCardTable.table-bordered th,
    .FOCRateCardTable.table-bordered td {
        border: 1px solid #dee2e6;
    }

    .FOCRateCardTable.table-bordered thead th,
    .FOCRateCardTable.table-bordered thead td {
        border-bottom-width: 2px;
    }

    .FOCRateCardTable.table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.075);
        cursor: pointer;
    }

    .FOCRateCardTable th[style*="cursor: pointer;"] {
        user-select: none;
    }

    .FOCRateCardTable th[style*="cursor: pointer;"]:hover {
        background-color: #e9ecef;
    }

    .FOCRateCardTable th[style*="cursor: pointer;"].ascending:after {
        content: '\25B2';
    }

    .FOCRateCardTable th[style*="cursor: pointer;"].descending:after {
        content: '\25BC';
    }

    .FOCRateCardTable.table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.05);
    }

    @@media (max-width: 768px) {
        .FOCRateCardTable {
            font-size: 0.8rem;
        }

        .table-responsive {
            overflow-x: auto;
        }
    }

    .e-grid.e-responsive .e-rowcell, .e-grid.e-responsive .e-headercelldiv {
        font-size: 12px !important;
        padding: 3px 5px !important;
    }
</style>
<div style="display:flex; gap:10px; align-items:center;">
    <h3>FOC Rate Cards </h3>
    <SfButton CssClass="e-primary" OnClick="OpenCreateForm">Create</SfButton>
</div>

<div class="row mb-2">
    <div class="col-2">
        <SfDatePicker @bind-Value="FilterDate" FloatLabelType="FloatLabelType.Always" Placeholder="Filter Date" Format="d MMM, yyyy"></SfDatePicker>
    </div>
    <div class="col-md">
        <SfDropDownList TValue="int?" TItem="EditionDto" DataSource="EditionList" Placeholder="Edition" AllowFiltering="true" FilterType="FilterType.Contains" @bind-Value="EditionId" ShowClearButton="true" FloatLabelType="FloatLabelType.Always">
            <DropDownListFieldSettings Value="Id" Text="Title"></DropDownListFieldSettings>
        </SfDropDownList>
    </div>
    <div class="col-md-1">
        <p style="height: 7px;"></p>
        <SfButton CssClass="e-info" OnClick="DoSearch">Search</SfButton>
    </div>
</div>

<div class="row">
    <div class="col">
        @if (FOCRateCardList.Any())
        {
            <SfGrid DataSource="FOCRateCardList" AllowSorting="true" AllowFiltering="true" Height="calc(100vh - 235px)">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn Field="Title" HeaderText="Title" TextAlign="TextAlign.Left" AutoFit="true">
                        <Template Context="context">
                            @{
                                var focRateCard = (FOCRateCardDto)context;
                                <a href="/setup/focratecards/@focRateCard.Id/edit">@focRateCard.Title</a>
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Field="DateFrom" HeaderText="Date From" Format="d MMM, yyyy" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="DateTo" HeaderText="Date To" Format="d MMM, yyyy" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="Status" HeaderText="Status" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="EditionStr" HeaderText="Editions" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="GovCategoryStr" HeaderText="Gov Category" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="GovAdTypeStr" HeaderText="Gov Ad Type" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="Priority" HeaderText="Priority" TextAlign="TextAlign.Left" AutoFit="true"></GridColumn>
                    <GridColumn Field="DiscountPer" HeaderText="Discount %" Format="##0.00" TextAlign="TextAlign.Right" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Actions" TextAlign="TextAlign.Center" AutoFit="true">
                        <Template Context="context">
                            @{
                                var focRateCard = (FOCRateCardDto)context;
                                <SfButton OnClick="@(() => DeleteFOCRateCard(focRateCard.Id))" CssClass="e-danger">Delete</SfButton>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        }
        else
        {
            <p>No FOC rate cards found.</p>
        }
    </div>
</div>


@code {
    private string CurrentSortColumn { get; set; } = string.Empty;
    private bool IsAscendingSort { get; set; } = true;
    public required DateTime FilterDate = DateTime.Today;
    private int? EditionId { get; set; }
    private List<EditionDto> EditionList = new();
    private List<FOCRateCardDto> FOCRateCardList = new();
    private bool _disposed;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        EditionList = await service.GetAllEditions();
        FOCRateCardList = await service.GetAllFOCRateCards(FilterDate, EditionId);
    }

    private void OpenCreateForm()
    {
        NavMgr.NavigateTo("/setup/focratecards/create");
    }

    private async Task DoSearch()
    {
        FOCRateCardList = await service.GetAllFOCRateCards(FilterDate, EditionId);
    }

    private void OpenEditForm(int id)
    {
        NavMgr.NavigateTo($"/setup/focratecards/{id}/edit");
    }

    private async Task DeleteFOCRateCard(int id)
    {
        if (_disposed) return;

        var confirm = await js.InvokeAsync<bool>("confirm", "Are you sure you want to delete this FOC Rate Card?");
        if (!confirm || _disposed) return;

        var result = await service.DeleteFOCRateCard(id);
        if (result == "OK" && !_disposed) await DoSearch();
    }

    private void SortBy(string columnName)
    {
        if (CurrentSortColumn == columnName)
        {
            IsAscendingSort = !IsAscendingSort;
        }
        else
        {
            CurrentSortColumn = columnName;
            IsAscendingSort = true;
        }

        switch (columnName)
        {
            case "Title":
                FOCRateCardList = IsAscendingSort ? FOCRateCardList.OrderBy(x => x.Title).ToList() : FOCRateCardList.OrderByDescending(x => x.Title).ToList();
                break;
            case "DateFrom":
                FOCRateCardList = IsAscendingSort ? FOCRateCardList.OrderBy(x => x.DateFrom).ToList() : FOCRateCardList.OrderByDescending(x => x.DateFrom).ToList();
                break;
            case "DateTo":
                FOCRateCardList = IsAscendingSort ? FOCRateCardList.OrderBy(x => x.DateTo).ToList() : FOCRateCardList.OrderByDescending(x => x.DateTo).ToList();
                break;
            case "Status":
                FOCRateCardList = IsAscendingSort ? FOCRateCardList.OrderBy(x => x.Status).ToList() : FOCRateCardList.OrderByDescending(x => x.Status).ToList();
                break;
            case "EditionStr":
                FOCRateCardList = IsAscendingSort ? FOCRateCardList.OrderBy(x => x.EditionStr).ToList() : FOCRateCardList.OrderByDescending(x => x.EditionStr).ToList();
                break;
            case "Priority":
                FOCRateCardList = IsAscendingSort ? FOCRateCardList.OrderBy(x => x.Priority).ToList() : FOCRateCardList.OrderByDescending(x => x.Priority).ToList();
                break;
            case "DiscountPer":
                FOCRateCardList = IsAscendingSort ? FOCRateCardList.OrderBy(x => x.DiscountPer).ToList() : FOCRateCardList.OrderByDescending(x => x.DiscountPer).ToList();
                break;
        }
    }

    private string GetSortIcon(string columnName)
    {
        if (CurrentSortColumn != columnName)
        {
            return string.Empty;
        }

        return IsAscendingSort ? "▲" : "▼";
    }

    public void Dispose()
    {
        _disposed = true;
    }

}