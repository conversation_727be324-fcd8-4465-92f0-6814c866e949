﻿@page "/setup/ratecards/apply"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inject RateCardService service
@rendermode InteractiveServer
<h3>Apply Rates</h3>
<div class="row">
    <div class="col-md-2">
        <SfDatePicker Placeholder="Date From" ShowClearButton="false" ShowTodayButton="true"
                      FloatLabelType="FloatLabelType.Always" @bind-Value="DateFrom"
                      Format="d MMM, yyyy"/>
    </div>
    <div class="col-md-2">
        <SfDatePicker Placeholder="Date From" ShowClearButton="false" ShowTodayButton="true"
                      FloatLabelType="FloatLabelType.Always" @bind-Value="DateTo"
                      Format="d MMM, yyyy"/>
    </div>
    <div class="col-md-2">
        <br/>
        @if (AdSpotsList.Any())
        {
            <SfButton CssClass="e-secondary" OnClick="ClearGrid">Clear</SfButton>
            <span>&nbsp;</span>
        }
        <SfButton CssClass="e-primary" OnClick="ApplyRateCards">Apply</SfButton>
    </div>
</div>
<div class="row">
    <div class="col">
        @if (AdSpotsList.Any())
        {
            <span>Total Records: <b>@AdSpotsList.Count</b></span>
            <span> - Time Taken: <b>@timeSpan.TotalSeconds.ToString("#####0.00") Seconds</b></span>
            <SfGrid Height="calc(100vh - 330px)" DataSource="AdSpotsList" AllowFiltering="true" AllowSorting="true" Width="100%" @ref="DefaultGrid" ID="Grid" AllowPaging="true">

                <GridFilterSettings Type="FilterType.Excel"/>

                <GridColumns>
                    <GridColumn HeaderText="Id" Field="@nameof(SpotDto.Id)" AutoFit="true"/>
                    <GridColumn HeaderText="Date" Field="@nameof(SpotDto.Date)" Format="d MMM, yyyy" AutoFit="true"/>
                    <GridColumn HeaderText="City" Field="@nameof(SpotDto.City)" AutoFit="true"/>
                    <GridColumn HeaderText="Edition" Field="@nameof(SpotDto.Edition)" AutoFit="true"/>
                    <GridColumn HeaderText="Publication" Field="@nameof(SpotDto.Publication)" AutoFit="true"/>
                    <GridColumn HeaderText="Sub Publication" Field="@nameof(SpotDto.SubPublication)" AutoFit="true"/>
                    <GridColumn HeaderText="Industry" Field="@nameof(SpotDto.Industry)" AutoFit="true"/>
                    <GridColumn HeaderText="Variant" Field="@nameof(SpotDto.Variant)" AutoFit="true"/>
                    <GridColumn HeaderText="Company" Field="@nameof(SpotDto.Company)" AutoFit="true"/>
                    <GridColumn HeaderText="Page Type" Field="@nameof(SpotDto.PageType)" AutoFit="true"/>
                    <GridColumn HeaderText="Ad Type" Field="@nameof(SpotDto.AdType)" AutoFit="true"/>
                    <GridColumn HeaderText="Color" Field="@nameof(SpotDto.Colour)" AutoFit="true"/>
                    <GridColumn HeaderText="Size1" Field="@nameof(SpotDto.Size1)" AutoFit="true"/>
                    <GridColumn HeaderText="Size2" Field="@nameof(SpotDto.Size2)" AutoFit="true"/>
                    <GridColumn HeaderText="Space" Field="@nameof(SpotDto.Space)" AutoFit="true"/>
                    <GridColumn HeaderText="Rate" Format="#######" Field="@nameof(SpotDto.Rate)" AutoFit="true"/>
                    <GridColumn HeaderText="Amount" Format="############" Field="@nameof(SpotDto.Amount)" AutoFit="true"/>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

@code {
    public DateOnly DateFrom { get; set; } = new(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day);


    public DateOnly DateTo { get; set; } = new(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day);
    private List<SpotDto> AdSpotsList = new();
    private TimeSpan timeSpan;
    private SfGrid<SpotDto> DefaultGrid;


    // override oninitialized method
    protected override async Task OnInitializedAsync()
    {
        await ApplyRateCards();
        // set DateFrom as first day of this year
        DateFrom = new DateOnly(DateTime.Now.Year, 1, 1);
    }

    private async Task ApplyRateCards()
    {
        var startTime = DateTime.Now;
        var msg = await service.ApplyRates(DateFrom, DateTo);
        AdSpotsList = await service.GetAdSpots(DateFrom, DateTo);
        var endTime = DateTime.Now;
        timeSpan = endTime - startTime;
    }

    private void ClearGrid()
    {
        AdSpotsList = new List<SpotDto>();
    }


}