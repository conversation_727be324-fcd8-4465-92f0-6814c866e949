﻿@using Color = Microsoft.FluentUI.AspNetCore.Components.Color
@using Icons = Microsoft.FluentUI.AspNetCore.Components.Icons
@inject NavigationManager NavMgr
@rendermode InteractiveServer

<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon" />
    <label for="navmenu-toggle" class="navmenu-icon"><FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Fill" /></label>
    <nav class="sitenav" aria-labelledby="main-menu" onclick="document.getElementById('navmenu-toggle').click();">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="expanded">
            <span style="cursor:pointer;display:flex;align-items:center;gap:8px;" @onclick="@(() => ForceNavigateTo("/"))">
                <FluentIcon Value="@(new Icons.Regular.Size20.Home())" Color="Color.Accent" /> Home
            </span>
            <span style="cursor:pointer;display:flex;align-items:center;gap:8px;" @onclick="@(() => ForceNavigateTo("/setup/ratecards"))">
                <FluentIcon Value="@(new Icons.Regular.Size20.CurrencyDollarRupee())" Color="Color.Accent" /> Rate Cards
            </span>
            <span style="cursor:pointer;display:flex;align-items:center;gap:8px;" @onclick="@(() => ForceNavigateTo("/setup/focratecards"))">
                <FluentIcon Value="@(new Icons.Regular.Size20.CurrencyDollarEuro())" Color="Color.Accent" /> FOC Rate Cards
            </span>
            <span style="cursor:pointer;display:flex;align-items:center;gap:8px;" @onclick="@(() => ForceNavigateTo("/setup/import-excel"))">
                <FluentIcon Value="@(new Icons.Regular.Size20.DocumentTable())" Color="Color.Accent" /> Import Excel
            </span>
            @*<FluentNavLink Href="/setup/ratecards/apply" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Money())" IconColor="Color.Accent">Apply Rate Cards</FluentNavLink>*@
            @*<FluentNavLink Href="/setup/ratecards/import" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Check())" IconColor="Color.Accent">Import Data</FluentNavLink>*@
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;
    private void ForceNavigateTo(string url)
    {
        NavMgr.NavigateTo(url, true);
    }
}
