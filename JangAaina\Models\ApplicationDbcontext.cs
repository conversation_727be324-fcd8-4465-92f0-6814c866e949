﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace JangAaina.Models;

public partial class ApplicationDbcontext : DbContext
{
    public ApplicationDbcontext(DbContextOptions<ApplicationDbcontext> options)
        : base(options)
    {
    }

    public virtual DbSet<DimAdType> DimAdTypes { get; set; }

    public virtual DbSet<DimBrand> DimBrands { get; set; }

    public virtual DbSet<DimCategory> DimCategories { get; set; }

    public virtual DbSet<DimCity> DimCities { get; set; }

    public virtual DbSet<DimColour> DimColours { get; set; }

    public virtual DbSet<DimCompany> DimCompanies { get; set; }

    public virtual DbSet<DimCountry> DimCountries { get; set; }

    public virtual DbSet<DimEdition> DimEditions { get; set; }

    public virtual DbSet<DimFrequency> DimFrequencies { get; set; }

    public virtual DbSet<DimGovAdType> DimGovAdTypes { get; set; }

    public virtual DbSet<DimGovCategory> DimGovCategories { get; set; }

    public virtual DbSet<DimIndustry> DimIndustries { get; set; }

    public virtual DbSet<DimLanguage> DimLanguages { get; set; }

    public virtual DbSet<DimMediaAgency> DimMediaAgencies { get; set; }

    public virtual DbSet<DimPageType> DimPageTypes { get; set; }

    public virtual DbSet<DimPublication> DimPublications { get; set; }

    public virtual DbSet<DimPublishType> DimPublishTypes { get; set; }

    public virtual DbSet<DimSubPublication> DimSubPublications { get; set; }

    public virtual DbSet<FactAdvertising> FactAdvertisings { get; set; }

    public virtual DbSet<FocrateCard> FocrateCards { get; set; }

    public virtual DbSet<FocrateCardAdType> FocrateCardAdTypes { get; set; }

    public virtual DbSet<FocrateCardCategory> FocrateCardCategories { get; set; }

    public virtual DbSet<FocrateCardCity> FocrateCardCities { get; set; }

    public virtual DbSet<FocrateCardClient> FocrateCardClients { get; set; }

    public virtual DbSet<FocrateCardEdition> FocrateCardEditions { get; set; }

    public virtual DbSet<FocrateCardGovAdType> FocrateCardGovAdTypes { get; set; }

    public virtual DbSet<FocrateCardGovCategory> FocrateCardGovCategories { get; set; }

    public virtual DbSet<FocrateCardIndustry> FocrateCardIndustries { get; set; }

    public virtual DbSet<FocrateCardSubPublication> FocrateCardSubPublications { get; set; }

    public virtual DbSet<RateCard> RateCards { get; set; }

    public virtual DbSet<RateCardAdType> RateCardAdTypes { get; set; }

    public virtual DbSet<RateCardCategory> RateCardCategories { get; set; }

    public virtual DbSet<RateCardCity> RateCardCities { get; set; }

    public virtual DbSet<RateCardClient> RateCardClients { get; set; }

    public virtual DbSet<RateCardDay> RateCardDays { get; set; }

    public virtual DbSet<RateCardEdition> RateCardEditions { get; set; }

    public virtual DbSet<RateCardGovAdType> RateCardGovAdTypes { get; set; }

    public virtual DbSet<RateCardGovCategory> RateCardGovCategories { get; set; }

    public virtual DbSet<RateCardIndustry> RateCardIndustries { get; set; }

    public virtual DbSet<RateCardPageAndColor> RateCardPageAndColors { get; set; }

    public virtual DbSet<RateCardSubPublication> RateCardSubPublications { get; set; }

    public virtual DbSet<RawDatum> RawData { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<DimAdType>(entity =>
        {
            entity.HasKey(e => e.AdTypeId).HasName("PK__DimAdTyp__3B1564B9DC51D990");

            entity.HasIndex(e => e.AdTypeName, "UQ__DimAdTyp__17E22BDDB956535D").IsUnique();

            entity.HasIndex(e => e.AdTypeName, "UX_DimAdTypes_AdTypeName").IsUnique();

            entity.Property(e => e.AdTypeId).HasColumnName("AdTypeID");
            entity.Property(e => e.AdTypeName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimBrand>(entity =>
        {
            entity.HasKey(e => e.BrandId).HasName("PK__DimBrand__DAD4F3BEB3944017");

            entity.HasIndex(e => e.BrandName, "IX_DimBrands_BrandName").HasFillFactor(90);

            entity.HasIndex(e => e.BrandName, "UQ__DimBrand__2206CE9BD3CEDC76").IsUnique();

            entity.HasIndex(e => e.BrandName, "UX_DimBrands_BrandName").IsUnique();

            entity.Property(e => e.BrandId).HasColumnName("BrandID");
            entity.Property(e => e.BrandName)
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimCategory>(entity =>
        {
            entity.HasKey(e => e.CategoryId).HasName("PK__DimCateg__19093A2B14C59351");

            entity.HasIndex(e => e.CategoryName, "UQ__DimCateg__8517B2E01ED4B397").IsUnique();

            entity.HasIndex(e => e.CategoryName, "UX_DimCategories_CategoryName").IsUnique();

            entity.Property(e => e.CategoryId).HasColumnName("CategoryID");
            entity.Property(e => e.CategoryName)
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimCity>(entity =>
        {
            entity.HasKey(e => e.CityId).HasName("PK__DimCitie__F2D21A96EF32807E");

            entity.HasIndex(e => e.CityName, "IX_DimCities_CityName").HasFillFactor(90);

            entity.HasIndex(e => new { e.CountryId, e.CityName }, "IX_DimCities_Composite").HasFillFactor(90);

            entity.HasIndex(e => e.CityName, "UQ__DimCitie__886159E5D571ECF4").IsUnique();

            entity.HasIndex(e => new { e.CityName, e.CountryId }, "UX_DimCities_CityName_CountryID").IsUnique();

            entity.Property(e => e.CityId).HasColumnName("CityID");
            entity.Property(e => e.CityName)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CountryId).HasColumnName("CountryID");

            entity.HasOne(d => d.Country).WithMany(p => p.DimCities)
                .HasForeignKey(d => d.CountryId)
                .HasConstraintName("FK__DimCities__Count__4222D4EF");
        });

        modelBuilder.Entity<DimColour>(entity =>
        {
            entity.HasKey(e => e.ColourId).HasName("PK__DimColou__4A2D247F58B51E78");

            entity.HasIndex(e => e.ColourName, "UQ__DimColou__72004244699B49C2").IsUnique();

            entity.HasIndex(e => e.ColourName, "UX_DimColours_ColourName").IsUnique();

            entity.Property(e => e.ColourId).HasColumnName("ColourID");
            entity.Property(e => e.ColourName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimCompany>(entity =>
        {
            entity.HasKey(e => e.CompanyId).HasName("PK__DimCompa__2D971C4C91CFCA26");

            entity.HasIndex(e => e.CompanyName, "UQ__DimCompa__9BCE05DC6C4C9258").IsUnique();

            entity.HasIndex(e => e.CompanyName, "UX_DimCompanies_CompanyName").IsUnique();

            entity.Property(e => e.CompanyId).HasColumnName("CompanyID");
            entity.Property(e => e.CompanyName)
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimCountry>(entity =>
        {
            entity.HasKey(e => e.CountryId).HasName("PK__DimCount__10D160BF0ED6F6F7");

            entity.HasIndex(e => e.CountryName, "IX_DimCountries_CountryName").HasFillFactor(90);

            entity.HasIndex(e => e.CountryName, "UQ__DimCount__E056F2012C816AD3").IsUnique();

            entity.HasIndex(e => e.CountryName, "UX_DimCountries_CountryName").IsUnique();

            entity.Property(e => e.CountryId).HasColumnName("CountryID");
            entity.Property(e => e.CountryName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimEdition>(entity =>
        {
            entity.HasKey(e => e.EditionId).HasName("PK__DimEditi__C76223634EC5AC23");

            entity.HasIndex(e => e.EditionName, "UQ__DimEditi__B6F7C4EEAB8206F3").IsUnique();

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.EditionName)
                .HasMaxLength(2000)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimFrequency>(entity =>
        {
            entity.HasKey(e => e.FrequencyId).HasName("PK__DimFrequ__592474B80666BE87");

            entity.HasIndex(e => e.FrequencyName, "UQ__DimFrequ__4C9E14F673BBD7DE").IsUnique();

            entity.HasIndex(e => e.FrequencyName, "UX_DimFrequencies_FrequencyName").IsUnique();

            entity.Property(e => e.FrequencyId).HasColumnName("FrequencyID");
            entity.Property(e => e.FrequencyName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimGovAdType>(entity =>
        {
            entity.HasKey(e => e.GovAdTypeId).HasName("PK__DimGovAd__88E9AB270C9C04DB");

            entity.HasIndex(e => e.GovAdTypeName, "UQ__DimGovAd__268CE1687847A5FE").IsUnique();

            entity.HasIndex(e => e.GovAdTypeName, "UX_DimGovAdTypes_GovAdTypeName").IsUnique();

            entity.Property(e => e.GovAdTypeId).HasColumnName("GovAdTypeID");
            entity.Property(e => e.GovAdTypeName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimGovCategory>(entity =>
        {
            entity.HasKey(e => e.GovCategoryId).HasName("PK__DimGovCa__D01F837CB7791041");

            entity.HasIndex(e => e.GovCategoryName, "UQ__DimGovCa__E62AE69C025EE5C3").IsUnique();

            entity.HasIndex(e => e.GovCategoryName, "UX_DimGovCategories_GovCategoryName").IsUnique();

            entity.Property(e => e.GovCategoryId).HasColumnName("GovCategoryID");
            entity.Property(e => e.GovCategoryName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimIndustry>(entity =>
        {
            entity.HasKey(e => e.IndustryId).HasName("PK__DimIndus__808DEC2C93E3ACFB");

            entity.HasIndex(e => e.IndustryName, "UQ__DimIndus__9678755F9A068F41").IsUnique();

            entity.HasIndex(e => e.IndustryName, "UX_DimIndustries_IndustryName").IsUnique();

            entity.Property(e => e.IndustryId).HasColumnName("IndustryID");
            entity.Property(e => e.IndustryName)
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimLanguage>(entity =>
        {
            entity.HasKey(e => e.LanguageId).HasName("PK__DimLangu__B938558BF4A58A46");

            entity.HasIndex(e => e.LanguageName, "UQ__DimLangu__E89C4A6AF2B1FCBB").IsUnique();

            entity.HasIndex(e => e.LanguageName, "UX_DimLanguages_LanguageName").IsUnique();

            entity.Property(e => e.LanguageId).HasColumnName("LanguageID");
            entity.Property(e => e.LanguageName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimMediaAgency>(entity =>
        {
            entity.HasKey(e => e.MediaAgencyId).HasName("PK__DimMedia__6E8734ED4F20694B");

            entity.HasIndex(e => e.MediaAgencyName, "UQ__DimMedia__30DA423F85275664").IsUnique();

            entity.HasIndex(e => e.MediaAgencyName, "UX_DimMediaAgencies_MediaAgencyName").IsUnique();

            entity.Property(e => e.MediaAgencyId).HasColumnName("MediaAgencyID");
            entity.Property(e => e.MediaAgencyName)
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimPageType>(entity =>
        {
            entity.HasKey(e => e.PageTypeId).HasName("PK__DimPageT__33FA9A657DE114EA");

            entity.HasIndex(e => e.PageTypeName, "UQ__DimPageT__AEDED0EE5EB83F24").IsUnique();

            entity.HasIndex(e => e.PageTypeName, "UX_DimPageTypes_PageTypeName").IsUnique();

            entity.Property(e => e.PageTypeId).HasColumnName("PageTypeID");
            entity.Property(e => e.PageTypeName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimPublication>(entity =>
        {
            entity.HasKey(e => e.PublicationId).HasName("PK__DimPubli__05E6DC58F3556FFF");

            entity.HasIndex(e => e.PublicationName, "IX_DimPublications_PublicationName").HasFillFactor(90);

            entity.HasIndex(e => e.PublicationName, "UQ__DimPubli__32E6F46FC5E7608F").IsUnique();

            entity.HasIndex(e => e.PublicationName, "UX_DimPublications_PublicationName").IsUnique();

            entity.Property(e => e.PublicationId).HasColumnName("PublicationID");
            entity.Property(e => e.PublicationName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimPublishType>(entity =>
        {
            entity.HasKey(e => e.PublishTypeId).HasName("PK__DimPubli__326A03B5D87995B7");

            entity.HasIndex(e => e.PublishTypeName, "UQ__DimPubli__1F0B24137BEDA850").IsUnique();

            entity.HasIndex(e => e.PublishTypeName, "UX_DimPublishTypes_PublishTypeName").IsUnique();

            entity.Property(e => e.PublishTypeId).HasColumnName("PublishTypeID");
            entity.Property(e => e.PublishTypeName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DimSubPublication>(entity =>
        {
            entity.HasKey(e => e.SubPublicationId).HasName("PK__DimSubPu__688151C83BE37AFE");

            entity.HasIndex(e => e.SubPublicationName, "UQ__DimSubPu__BC79FF914339C237").IsUnique();

            entity.HasIndex(e => e.SubPublicationName, "UX_DimSubPublications_SubPublicationName").IsUnique();

            entity.Property(e => e.SubPublicationId).HasColumnName("SubPublicationID");
            entity.Property(e => e.SubPublicationName)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<FactAdvertising>(entity =>
        {
            entity.HasKey(e => e.FactAdvertisingId).HasName("PK_FactAdvertising_FactAdvertisingID");

            entity.ToTable("FactAdvertising");

            entity.HasIndex(e => new { e.DateKey, e.SubPublicationId, e.ColourId, e.PageTypeId }, "IX_FactAdvertising_DateKey");

            entity.Property(e => e.FactAdvertisingId)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("FactAdvertisingID");
            entity.Property(e => e.AdTypeId).HasColumnName("AdTypeID");
            entity.Property(e => e.Amount).HasColumnType("money");
            entity.Property(e => e.AmountAfterDiscount).HasColumnType("money");
            entity.Property(e => e.BrandId).HasColumnName("BrandID");
            entity.Property(e => e.Caption)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.CategoryId).HasColumnName("CategoryID");
            entity.Property(e => e.CityId).HasColumnName("CityID");
            entity.Property(e => e.ColourId).HasColumnName("ColourID");
            entity.Property(e => e.CompanyId).HasColumnName("CompanyID");
            entity.Property(e => e.CountryId).HasColumnName("CountryID");
            entity.Property(e => e.DayName)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.FocrateCardId).HasColumnName("FOCRateCardId");
            entity.Property(e => e.FrequencyId).HasColumnName("FrequencyID");
            entity.Property(e => e.GovAdTypeId).HasColumnName("GovAdTypeID");
            entity.Property(e => e.GovCategoryId).HasColumnName("GovCategoryID");
            entity.Property(e => e.IndustryId).HasColumnName("IndustryID");
            entity.Property(e => e.LanguageId).HasColumnName("LanguageID");
            entity.Property(e => e.MediaAgencyId).HasColumnName("MediaAgencyID");
            entity.Property(e => e.PageTypeId).HasColumnName("PageTypeID");
            entity.Property(e => e.PublicationId).HasColumnName("PublicationID");
            entity.Property(e => e.PublishTypeId).HasColumnName("PublishTypeID");
            entity.Property(e => e.Rate).HasColumnType("money");
            entity.Property(e => e.SubPublicationId).HasColumnName("SubPublicationID");
            entity.Property(e => e.Variant)
                .HasMaxLength(200)
                .IsUnicode(false);

            entity.HasOne(d => d.AdType).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.AdTypeId)
                .HasConstraintName("FK__FactAdver__AdTyp__2116E6DF");

            entity.HasOne(d => d.Brand).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.BrandId)
                .HasConstraintName("FK__FactAdver__Brand__2022C2A6");

            entity.HasOne(d => d.Category).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.CategoryId)
                .HasConstraintName("FK__FactAdver__Categ__1F2E9E6D");

            entity.HasOne(d => d.City).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.CityId)
                .HasConstraintName("FK__FactAdver__CityI__1E3A7A34");

            entity.HasOne(d => d.Colour).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.ColourId)
                .HasConstraintName("FK__FactAdver__Colou__1D4655FB");

            entity.HasOne(d => d.Company).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.CompanyId)
                .HasConstraintName("FK__FactAdver__Compa__1C5231C2");

            entity.HasOne(d => d.Country).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.CountryId)
                .HasConstraintName("FK__FactAdver__Count__1B5E0D89");

            entity.HasOne(d => d.FocrateCard).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.FocrateCardId)
                .HasConstraintName("FK__FactAdver__FOCRa__361203C5");

            entity.HasOne(d => d.Frequency).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.FrequencyId)
                .HasConstraintName("FK__FactAdver__Frequ__1A69E950");

            entity.HasOne(d => d.GovAdType).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.GovAdTypeId)
                .HasConstraintName("FK__FactAdver__GovAd__1975C517");

            entity.HasOne(d => d.GovCategory).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.GovCategoryId)
                .HasConstraintName("FK__FactAdver__GovCa__1881A0DE");

            entity.HasOne(d => d.Industry).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.IndustryId)
                .HasConstraintName("FK__FactAdver__Indus__178D7CA5");

            entity.HasOne(d => d.Language).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.LanguageId)
                .HasConstraintName("FK__FactAdver__Langu__1699586C");

            entity.HasOne(d => d.MediaAgency).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.MediaAgencyId)
                .HasConstraintName("FK__FactAdver__Media__15A53433");

            entity.HasOne(d => d.PageType).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.PageTypeId)
                .HasConstraintName("FK__FactAdver__PageT__14B10FFA");

            entity.HasOne(d => d.Publication).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.PublicationId)
                .HasConstraintName("FK__FactAdver__Publi__13BCEBC1");

            entity.HasOne(d => d.PublishType).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.PublishTypeId)
                .HasConstraintName("FK__FactAdver__Publi__12C8C788");

            entity.HasOne(d => d.SubPublication).WithMany(p => p.FactAdvertisings)
                .HasForeignKey(d => d.SubPublicationId)
                .HasConstraintName("FK__FactAdver__SubPu__11D4A34F");
        });

        modelBuilder.Entity<FocrateCard>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__FOCRateC__3214EC07B86D2262");

            entity.ToTable("FOCRateCards");

            entity.HasIndex(e => e.Title, "UQ__FOCRateC__2CB664DC77B6EA0D").IsUnique();

            entity.Property(e => e.AllCities).HasDefaultValue(true);
            entity.Property(e => e.AllClients).HasDefaultValue(true);
            entity.Property(e => e.AllGovAdType).HasDefaultValue(true);
            entity.Property(e => e.AllGovCategories).HasDefaultValue(true);
            entity.Property(e => e.AllIndustries).HasDefaultValue(true);
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DateFrom).HasColumnType("datetime");
            entity.Property(e => e.DateTo).HasColumnType("datetime");
            entity.Property(e => e.DiscountPer).HasColumnType("decimal(18, 0)");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Priority).HasDefaultValue(1);
            entity.Property(e => e.Title)
                .HasMaxLength(1000)
                .IsUnicode(false);
        });

        modelBuilder.Entity<FocrateCardAdType>(entity =>
        {
            entity.HasKey(e => new { e.FocrateCardId, e.GovAdTypeId }).HasName("PK__FOCRateC__CC0AEB99F3A7B71A");

            entity.ToTable("FOCRateCardAdTypes");

            entity.HasIndex(e => new { e.FocrateCardId, e.GovAdTypeId }, "IX_FOCRateCardAdTypes_RateCardId");

            entity.Property(e => e.FocrateCardId).HasColumnName("FOCRateCardId");
            entity.Property(e => e.CreateDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FocrateCard).WithMany(p => p.FocrateCardAdTypes)
                .HasForeignKey(d => d.FocrateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__FOCRa__3CBF0154");

            entity.HasOne(d => d.GovAdType).WithMany(p => p.FocrateCardAdTypes)
                .HasForeignKey(d => d.GovAdTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__GovAd__3DB3258D");
        });

        modelBuilder.Entity<FocrateCardCategory>(entity =>
        {
            entity.HasKey(e => new { e.FocrateCardId, e.GovCategoryId }).HasName("PK__FOCRateC__798589E411AA8740");

            entity.ToTable("FOCRateCardCategories");

            entity.HasIndex(e => new { e.FocrateCardId, e.GovCategoryId }, "IX_FOCRateCardCategories_RateCardId");

            entity.Property(e => e.FocrateCardId).HasColumnName("FOCRateCardId");
            entity.Property(e => e.CreateDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FocrateCard).WithMany(p => p.FocrateCardCategories)
                .HasForeignKey(d => d.FocrateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__FOCRa__4183B671");

            entity.HasOne(d => d.GovCategory).WithMany(p => p.FocrateCardCategories)
                .HasForeignKey(d => d.GovCategoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__GovCa__436BFEE3");
        });

        modelBuilder.Entity<FocrateCardCity>(entity =>
        {
            entity.HasKey(e => new { e.FocrateCardId, e.CityId }).HasName("PK__FOCRateC__DBA9509A3AFFBC9E");

            entity.ToTable("FOCRateCardCities");

            entity.HasIndex(e => new { e.FocrateCardId, e.CityId }, "IX_FOCRateCardCities_RateCardId");

            entity.Property(e => e.FocrateCardId).HasColumnName("FOCRateCardId");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.City).WithMany(p => p.FocrateCardCities)
                .HasForeignKey(d => d.CityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__CityI__093F5D4E");

            entity.HasOne(d => d.FocrateCard).WithMany(p => p.FocrateCardCities)
                .HasForeignKey(d => d.FocrateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__FOCRa__0B27A5C0");
        });

        modelBuilder.Entity<FocrateCardClient>(entity =>
        {
            entity.HasKey(e => new { e.FocrateCardId, e.ClientId }).HasName("PK__FOCRateC__FAE3908F76F22342");

            entity.ToTable("FOCRateCardClients");

            entity.HasIndex(e => new { e.FocrateCardId, e.ClientId }, "IX_FOCRateCardClient_RateCardId");

            entity.Property(e => e.FocrateCardId).HasColumnName("FOCRateCardId");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.Client).WithMany(p => p.FocrateCardClients)
                .HasForeignKey(d => d.ClientId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__Clien__6F7F8B4B");

            entity.HasOne(d => d.FocrateCard).WithMany(p => p.FocrateCardClients)
                .HasForeignKey(d => d.FocrateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__FOCRa__6D9742D9");
        });

        modelBuilder.Entity<FocrateCardEdition>(entity =>
        {
            entity.HasKey(e => new { e.FocrateCardId, e.EditionId }).HasName("PK__FOCRateC__88F2531BDF1CEFD5");

            entity.ToTable("FOCRateCardEditions");

            entity.HasIndex(e => new { e.FocrateCardId, e.EditionId }, "IX_FOCRateCardEditions_RateCardId");

            entity.Property(e => e.FocrateCardId).HasColumnName("FOCRateCardId");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.Edition).WithMany(p => p.FocrateCardEditions)
                .HasForeignKey(d => d.EditionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__Editi__589C25F3");

            entity.HasOne(d => d.FocrateCard).WithMany(p => p.FocrateCardEditions)
                .HasForeignKey(d => d.FocrateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__FOCRa__59904A2C");
        });

        modelBuilder.Entity<FocrateCardGovAdType>(entity =>
        {
            entity.HasKey(e => new { e.FocrateCardId, e.GovAdTypeId }).HasName("PK__FOCRateC__CC0AEB997B36CE47");

            entity.ToTable("FOCRateCardGovAdTypes");

            entity.HasIndex(e => new { e.FocrateCardId, e.GovAdTypeId }, "IX_FOCRateCardGovAdTypes_RateCardId");

            entity.Property(e => e.FocrateCardId).HasColumnName("FOCRateCardId");
            entity.Property(e => e.CreateDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FocrateCard).WithMany(p => p.FocrateCardGovAdTypes)
                .HasForeignKey(d => d.FocrateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__FOCRa__5006DFF2");

            entity.HasOne(d => d.GovAdType).WithMany(p => p.FocrateCardGovAdTypes)
                .HasForeignKey(d => d.GovAdTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__GovAd__4F12BBB9");
        });

        modelBuilder.Entity<FocrateCardGovCategory>(entity =>
        {
            entity.HasKey(e => new { e.FocrateCardId, e.GovCategoryId }).HasName("PK__FOCRateC__798589E4E5AD69B3");

            entity.ToTable("FOCRateCardGovCategories");

            entity.HasIndex(e => new { e.FocrateCardId, e.GovCategoryId }, "IX_FOCRateCardGovCategories_RateCardId");

            entity.Property(e => e.FocrateCardId).HasColumnName("FOCRateCardId");
            entity.Property(e => e.CreateDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FocrateCard).WithMany(p => p.FocrateCardGovCategories)
                .HasForeignKey(d => d.FocrateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__FOCRa__54CB950F");

            entity.HasOne(d => d.GovCategory).WithMany(p => p.FocrateCardGovCategories)
                .HasForeignKey(d => d.GovCategoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__GovCa__53D770D6");
        });

        modelBuilder.Entity<FocrateCardIndustry>(entity =>
        {
            entity.HasKey(e => new { e.FocrateCardId, e.IndustryId }).HasName("PK__FOCRateC__7C8CAFF1675A18AA");

            entity.ToTable("FOCRateCardIndustries");

            entity.HasIndex(e => new { e.FocrateCardId, e.IndustryId }, "IX_FOCRateCardIndustry_RateCardId");

            entity.Property(e => e.FocrateCardId).HasColumnName("FOCRateCardId");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FocrateCard).WithMany(p => p.FocrateCardIndustries)
                .HasForeignKey(d => d.FocrateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__FOCRa__68D28DBC");

            entity.HasOne(d => d.Industry).WithMany(p => p.FocrateCardIndustries)
                .HasForeignKey(d => d.IndustryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__Indus__69C6B1F5");
        });

        modelBuilder.Entity<FocrateCardSubPublication>(entity =>
        {
            entity.HasKey(e => new { e.FocrateCardId, e.SubPublicationId }).HasName("PK__FOCRateC__320C643F0C6D5366");

            entity.ToTable("FOCRateCardSubPublications");

            entity.HasIndex(e => new { e.FocrateCardId, e.SubPublicationId }, "IX_FOCRateCardSubPublications_RateCardId");

            entity.Property(e => e.FocrateCardId).HasColumnName("FOCRateCardId");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FocrateCard).WithMany(p => p.FocrateCardSubPublications)
                .HasForeignKey(d => d.FocrateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__FOCRa__6501FCD8");

            entity.HasOne(d => d.SubPublication).WithMany(p => p.FocrateCardSubPublications)
                .HasForeignKey(d => d.SubPublicationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FOCRateCa__SubPu__640DD89F");
        });

        modelBuilder.Entity<RateCard>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__RateCard__3214EC07AEB24887");

            entity.HasIndex(e => new { e.Priority, e.IsActive, e.IsDeleted, e.DateFrom, e.DateTo }, "IX_RateCards_Priority_IsActive_IsDeleted_DateRange");

            entity.HasIndex(e => e.Title, "UX_RateCards_ActiveTitle")
                .IsUnique()
                .HasFilter("([IsActive]=(1) AND [IsDeleted]=(0))");

            entity.Property(e => e.AllAdTypes).HasDefaultValue(true);
            entity.Property(e => e.AllCategories).HasDefaultValue(true);
            entity.Property(e => e.AllCities).HasDefaultValue(true);
            entity.Property(e => e.AllClients).HasDefaultValue(true);
            entity.Property(e => e.AllDays).HasDefaultValue(true);
            entity.Property(e => e.AllEditions).HasDefaultValue(true);
            entity.Property(e => e.AllGovAdTypes).HasDefaultValue(true);
            entity.Property(e => e.AllGovCategories).HasDefaultValue(true);
            entity.Property(e => e.AllIndustries).HasDefaultValue(true);
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DateFrom).HasColumnType("datetime");
            entity.Property(e => e.DateTo).HasColumnType("datetime");
            entity.Property(e => e.Description)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.PremiumPercentage).HasColumnType("money");
            entity.Property(e => e.Priority).HasDefaultValue(1);
            entity.Property(e => e.Title)
                .HasMaxLength(1000)
                .IsUnicode(false);
        });

        modelBuilder.Entity<RateCardAdType>(entity =>
        {
            entity.HasKey(e => new { e.RateCardId, e.AdTypeId }).HasName("PK__RateCard__8392CBFEF8E099E0");

            entity.HasIndex(e => new { e.RateCardId, e.AdTypeId }, "IX_RateCardAdTypes_RateCardId");

            entity.HasOne(d => d.AdType).WithMany(p => p.RateCardAdTypes)
                .HasForeignKey(d => d.AdTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardA__AdTyp__3B40CD36");

            entity.HasOne(d => d.RateCard).WithMany(p => p.RateCardAdTypes)
                .HasForeignKey(d => d.RateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardA__RateC__3A4CA8FD");
        });

        modelBuilder.Entity<RateCardCategory>(entity =>
        {
            entity.HasKey(e => new { e.RateCardId, e.CategoryId }).HasName("PK__RateCard__A1B30E13A02145E8");

            entity.HasIndex(e => new { e.RateCardId, e.CategoryId }, "IX_RateCardCategories_RateCardId");

            entity.HasOne(d => d.Category).WithMany(p => p.RateCardCategories)
                .HasForeignKey(d => d.CategoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardC__Categ__45BE5BA9");

            entity.HasOne(d => d.RateCard).WithMany(p => p.RateCardCategories)
                .HasForeignKey(d => d.RateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardC__RateC__44CA3770");
        });

        modelBuilder.Entity<RateCardCity>(entity =>
        {
            entity.HasKey(e => new { e.RateCardId, e.CityId }).HasName("PK__RateCard__7F0EBC040EBAE11E");

            entity.HasIndex(e => new { e.RateCardId, e.CityId }, "IX_RateCardCities_RateCardId");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.City).WithMany(p => p.RateCardCities)
                .HasForeignKey(d => d.CityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardC__CityI__37703C52");

            entity.HasOne(d => d.RateCard).WithMany(p => p.RateCardCities)
                .HasForeignKey(d => d.RateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardC__RateC__367C1819");
        });

        modelBuilder.Entity<RateCardClient>(entity =>
        {
            entity.HasKey(e => new { e.RateCardId, e.ClientId }).HasName("PK__RateCard__5E447C11F077E4E5");

            entity.HasIndex(e => new { e.RateCardId, e.ClientId }, "IX_RateCardClients_RateCardId");

            entity.HasOne(d => d.Client).WithMany(p => p.RateCardClients)
                .HasForeignKey(d => d.ClientId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardC__Clien__3D2915A8");

            entity.HasOne(d => d.RateCard).WithMany(p => p.RateCardClients)
                .HasForeignKey(d => d.RateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardC__RateC__3C34F16F");
        });

        modelBuilder.Entity<RateCardDay>(entity =>
        {
            entity.HasKey(e => new { e.RateCardId, e.DayNumber }).HasName("PK_RateCardDays2");

            entity.HasIndex(e => new { e.RateCardId, e.DayNumber }, "IX_RateCardDays_RateCardId");
        });

        modelBuilder.Entity<RateCardEdition>(entity =>
        {
            entity.HasKey(e => new { e.RateCardId, e.EditionId }).HasName("PK__RateCard__2C55BF853A93396F");

            entity.HasIndex(e => new { e.RateCardId, e.EditionId }, "IX_RateCardEditions_RateCardId");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.Edition).WithMany(p => p.RateCardEditions)
                .HasForeignKey(d => d.EditionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardE__Editi__719CDDE7");

            entity.HasOne(d => d.RateCard).WithMany(p => p.RateCardEditions)
                .HasForeignKey(d => d.RateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardE__RateC__70A8B9AE");
        });

        modelBuilder.Entity<RateCardGovAdType>(entity =>
        {
            entity.HasKey(e => new { e.RateCardId, e.GovAdTypeId }).HasName("PK__RateCard__68AD0707A3FB87C6");

            entity.HasIndex(e => new { e.RateCardId, e.GovAdTypeId }, "IX_RateCardGovAdTypes_RateCardId");

            entity.Property(e => e.CreateDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.GovAdType).WithMany(p => p.RateCardGovAdTypes)
                .HasForeignKey(d => d.GovAdTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardG__GovAd__2CF2ADDF");

            entity.HasOne(d => d.RateCard).WithMany(p => p.RateCardGovAdTypes)
                .HasForeignKey(d => d.RateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardG__RateC__2BFE89A6");
        });

        modelBuilder.Entity<RateCardGovCategory>(entity =>
        {
            entity.HasKey(e => new { e.RateCardId, e.GovCategoryId }).HasName("PK__RateCard__DD22657A5ABEF9AB");

            entity.HasIndex(e => new { e.RateCardId, e.GovCategoryId }, "IX_RateCardGovCategories_RateCardId");

            entity.Property(e => e.CreateDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.GovCategory).WithMany(p => p.RateCardGovCategories)
                .HasForeignKey(d => d.GovCategoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardG__GovCa__32AB8735");

            entity.HasOne(d => d.RateCard).WithMany(p => p.RateCardGovCategories)
                .HasForeignKey(d => d.RateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardG__RateC__31B762FC");
        });

        modelBuilder.Entity<RateCardIndustry>(entity =>
        {
            entity.HasKey(e => new { e.RateCardId, e.IndustryId }).HasName("PK__RateCard__D82B436F6F67CFCE");

            entity.HasIndex(e => new { e.RateCardId, e.IndustryId }, "IX_RateCardIndustries_RateCardId");

            entity.HasOne(d => d.Industry).WithMany(p => p.RateCardIndustries)
                .HasForeignKey(d => d.IndustryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardI__Indus__395884C4");

            entity.HasOne(d => d.RateCard).WithMany(p => p.RateCardIndustries)
                .HasForeignKey(d => d.RateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardI__RateC__3864608B");
        });

        modelBuilder.Entity<RateCardPageAndColor>(entity =>
        {
            entity.HasKey(e => new { e.RateCardId, e.PageTypeId, e.ColorId }).HasName("PK__RateCard__0F919370431E6012");

            entity.HasIndex(e => new { e.RateCardId, e.PageTypeId, e.ColorId, e.Rate }, "IX_RateCardPageAndColors_RateCardId");

            entity.Property(e => e.Rate).HasColumnType("money");

            entity.HasOne(d => d.Color).WithMany(p => p.RateCardPageAndColors)
                .HasForeignKey(d => d.ColorId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardP__Color__40058253");

            entity.HasOne(d => d.PageType).WithMany(p => p.RateCardPageAndColors)
                .HasForeignKey(d => d.PageTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardP__PageT__3F115E1A");

            entity.HasOne(d => d.RateCard).WithMany(p => p.RateCardPageAndColors)
                .HasForeignKey(d => d.RateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardP__RateC__3E1D39E1");
        });

        modelBuilder.Entity<RateCardSubPublication>(entity =>
        {
            entity.HasKey(e => new { e.RateCardId, e.SubPublicationId }).HasName("PK__RateCard__96AB88A1D4B0E6A3");

            entity.HasIndex(e => new { e.RateCardId, e.SubPublicationId }, "IX_RateCardSubPublications_RateCardId");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.RateCard).WithMany(p => p.RateCardSubPublications)
                .HasForeignKey(d => d.RateCardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardS__RateC__40F9A68C");

            entity.HasOne(d => d.SubPublication).WithMany(p => p.RateCardSubPublications)
                .HasForeignKey(d => d.SubPublicationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RateCardS__SubPu__41EDCAC5");
        });

        modelBuilder.Entity<RawDatum>(entity =>
        {
            entity.HasNoKey();

            entity.HasIndex(e => new { e.Date, e.Company, e.Brand, e.SubPublication }, "IX_RawData_CityLookup").HasFillFactor(90);

            entity.HasIndex(e => new { e.Date, e.SubPublication, e.Company, e.Caption, e.Variant, e.Brand, e.Category, e.Colour, e.Industry, e.AdType }, "IX_RawData_EditionGrouping").HasFillFactor(90);

            entity.HasIndex(e => new { e.Company, e.Brand, e.Date, e.SubPublication, e.Caption, e.Variant, e.Category, e.Colour, e.Industry, e.AdType }, "IX_RawData_EditionGrouping_Alt").HasFillFactor(90);

            entity.Property(e => e.AdType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("Ad Type");
            entity.Property(e => e.Brand)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Caption)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Category)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.City)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Colour)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("COLOUR");
            entity.Property(e => e.Company)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Country)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Day)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Edition)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.Frequency)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.GovAdType)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.GovCategory)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Industry)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Language)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.MediaAgency)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("Media Agency");
            entity.Property(e => e.PageNo).HasColumnName("Page No.");
            entity.Property(e => e.PageType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("Page Type");
            entity.Property(e => e.Publication)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.PublishType)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.SubPublication)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("Sub Publication");
            entity.Property(e => e.Variant)
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}