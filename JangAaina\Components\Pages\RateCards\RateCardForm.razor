@page "/setup/ratecards/create"
@page "/setup/ratecards/{RateCardId:int}/edit"
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@rendermode InteractiveServer
@implements IAsyncDisposable

@inject RateCardService service
<style>
    td {padding:2px;}
</style>
<SfToast @ref="_toastObj"></SfToast>
<h3>Rate Card Detail</h3>
<EditForm FormName="rate_card_form" OnValidSubmit="SaveRateCard" Model="rateCard">
    <DataAnnotationsValidator></DataAnnotationsValidator>
    <ValidationSummary></ValidationSummary>
    <div class="row">
        <div class="col">
            <SfTextBox FloatLabelType="FloatLabelType.Always"
                       @bind-Value="rateCard.Title"
                       Placeholder="Title">
            </SfTextBox>
        </div>
    </div>
    <div class="row mt-2">
        <div class="col-md">
            <SfDatePicker @bind-Value="rateCard.DateFrom"
                          Placeholder="Date - From"
                          FloatLabelType="FloatLabelType.Always"
                          Format="d MMM, yyyy">
            </SfDatePicker>
        </div>
        <div class="col-md">
            <SfDatePicker @bind-Value="rateCard.DateTo"
                          Placeholder="Date - To"
                          FloatLabelType="FloatLabelType.Always"
                          Format="d MMM, yyyy">
            </SfDatePicker>
        </div>
        <div class="col-md-2">
            <SfNumericTextBox FloatLabelType="FloatLabelType.Always"
                              Placeholder="Priority"
                              ShowClearButton="false"
                              Min="1" Max="50"
                              Decimals="0"
                              ShowSpinButton="false"
                              @bind-Value="rateCard.Priority">
            </SfNumericTextBox>
        </div>
        <div class="col-md-2">
            <p style="height: 12px;"></p>
            <div style="display: flex; align-items: center; gap:  6px">
                <SfSwitch @bind-Checked="rateCard.IsActive"></SfSwitch> @rateCard.Status
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md">

            <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains" FloatLabelType="FloatLabelType.Always" Placeholder="Publication" @bind-Value="@rateCard.SelectedSubPublicationList" DataSource="SubPublicationList">
                <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
            </SfMultiSelect>
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="rateCard.AllCities"></SfCheckBox> All Cities
            @if (rateCard.AllCities == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains" 
                               FloatLabelType="FloatLabelType.Always" 
                               Placeholder="City" @bind-Value="@rateCard.SelectedCitiesList" DataSource="CitiesList">
                    <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="rateCard.AllEditions"></SfCheckBox> All Editions
            @if (rateCard.AllEditions == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains" 
                               FloatLabelType="FloatLabelType.Always" Placeholder="Edition" 
                               @bind-Value="@rateCard.SelectedEditionList" DataSource="EditionList">
                    <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>


    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="rateCard.AllDays"></SfCheckBox> All Days
            @if (rateCard.AllDays == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains" FloatLabelType="FloatLabelType.Always" Placeholder="Days" @bind-Value="@rateCard.SelectedDaysList" DataSource="DaysList">
                    <MultiSelectFieldSettings Value="Id" Text="Name"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="rateCard.AllAdTypes"></SfCheckBox> All Ad Types
            @if (rateCard.AllAdTypes == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains"
                               FloatLabelType="FloatLabelType.Always" Placeholder="Ad Type"
                               @bind-Value="@rateCard.SelectedAdTypeList" DataSource="AdTypeList">
                    <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="rateCard.AllCategories"></SfCheckBox> All Categories
            @if (rateCard.AllCategories == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains"
                               FloatLabelType="FloatLabelType.Always" Placeholder="Category"
                               @bind-Value="@rateCard.SelectedCategoryList" DataSource="CategoryList">
                    <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="rateCard.AllIndustries"></SfCheckBox> All Industries
            @if (rateCard.AllIndustries == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains"
                               FloatLabelType="FloatLabelType.Always" Placeholder="Industry"
                               @bind-Value="@rateCard.SelectedIndustryList" DataSource="IndustryList">
                    <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>
    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="rateCard.AllGovCategories"></SfCheckBox> All Categories (Gov)
            @if (rateCard.AllGovCategories == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains" EnableVirtualization="true"
                               FloatLabelType="FloatLabelType.Always" Placeholder="Gov Categoey"
                               @bind-Value="@rateCard.SelectedGovCategoryList" DataSource="GovCategoryList">
                    <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>


    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="rateCard.AllGovAdTypes"></SfCheckBox> All Ad Types (Gov)
            @if (rateCard.AllGovAdTypes == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains" EnableVirtualization="true"
                               FloatLabelType="FloatLabelType.Always" Placeholder="Ad Type Categories"
                               @bind-Value="@rateCard.SelectedGovAdTypes" DataSource="AllGovAdTypesList">
                    <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>


    <div class="row mt-2">
        <div class="col-md">
            <SfCheckBox @bind-Checked="rateCard.AllClients"></SfCheckBox> All Clients
            @if (rateCard.AllClients == false)
            {
                <SfMultiSelect AllowFiltering="true" FilterType="FilterType.Contains" EnableVirtualization="true"
                               FloatLabelType="FloatLabelType.Always" Placeholder="Client"
                               @bind-Value="@rateCard.SelectedClientList" DataSource="ClientList">
                    <MultiSelectFieldSettings Value="Id" Text="Title"></MultiSelectFieldSettings>
                </SfMultiSelect>
            }
        </div>
    </div>
<div class="row mt-2">
    <div class="col-md" style="display:flex; align-items:flex-end;gap: 10px;">
        <h3 style="margin-bottom:0;">Rate Detail</h3>
   </div>
</div>
<div class="row mt-2">
    <div class="col-md">&nbsp;</div>
    <div class="col-md">
        <SfNumericTextBox Placeholder="RODP" Width="150px" Decimals="2" Format="###,##0.00" ShowClearButton="true" ShowSpinButton="false" @bind-Value="fixedRate" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
    </div>
    <div class="col-md">
        @* Color Premium *@
        <SfNumericTextBox Placeholder="Color %" 
                          Width="150px" Decimals="2" 
                          Format="##0.00" 
                          ShowClearButton="true" ShowSpinButton="false" @bind-Value="fixColor" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
    </div>
    <div class="col-md">
        @* Black All Color Premium *@
        <SfNumericTextBox Placeholder="Spot Color %" 
                          Width="150px" 
                          Decimals="2" 
                          Format="##0.00" ShowClearButton="true" ShowSpinButton="false" @bind-Value="fixSpotColor" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
    </div>
    <div class="col-md">
        @* Spot Color Premium *@
        <SfNumericTextBox Placeholder="All Color %" 
                          Width="150px" 
                          Decimals="2" Format="##0.00" Min="0" ShowClearButton="true" ShowSpinButton="false" @bind-Value="fixAllColor" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
    </div>
    <div class="col-md" style=" display: flex; flex-direction: column-reverse;">
        <SfButton Style="width:120px" CssClass="e-primary" OnClick="FillFixRate" type="button">Apply Fix Rate</SfButton>
    </div>
</div>

    <div class="row mt-2">
        <div class="col-md">
            <table class="table">
                <thead>
                    <tr>
                        <th>Page Type</th>
                        <th>Premium %</th>
                        <th>Black and White</th>
                        <th>Color</th>
                        <th>Spot Color</th>
                        <th>All Color</th>
                    </tr>
                </thead>
                @foreach (var p in rateCard.Rates)
                {
                    <tr>
                        <td>@p.PageType</td>
                        <td>
                            <SfNumericTextBox Placeholder="Premium %"
                                              FloatLabelType="FloatLabelType.Never"
                                              @bind-Value="p.PremiumPercentage"
                                              Min="0" 
                                              Decimals="2" ShowSpinButton="false" Format="##0.00">
                            </SfNumericTextBox>
                        </td>
                        <td>
                            <SfNumericTextBox Placeholder="Black and White"
                                              FloatLabelType="FloatLabelType.Never"
                                              @bind-Value="p.BlackNWhite"
                                              Decimals="2" ShowSpinButton="false" Format="########0.00">
                            </SfNumericTextBox>
                        </td>
                        <td>
                            <SfNumericTextBox Placeholder="Color" FloatLabelType="FloatLabelType.Never"
                                              @bind-Value="p.Colour" Min="0" 
                                              Decimals="2" ShowSpinButton="false" Format="####0.00">
                            </SfNumericTextBox>
                        </td>
                        <td>
                            <SfNumericTextBox Placeholder="Spot Color" FloatLabelType="FloatLabelType.Never"
                                              @bind-Value="p.SpotColour" Min="0" 
                                              Decimals="2" ShowSpinButton="false" Format="########0.00">
                            </SfNumericTextBox>
                        </td>
                        <td>
                            <SfNumericTextBox Placeholder="All Color" FloatLabelType="FloatLabelType.Never"
                                              @bind-Value="p.AllColour" Min="0" 
                                              Decimals="2" ShowSpinButton="false" Format="########0.00">
                            </SfNumericTextBox>
                        </td>
                    </tr>
                }
            </table>
        </div>
    </div>
    <!--<div class="row mt-2">
        <div class="col-md">-->
    @* create a Syncfusion Numeric Text box which will be used for storing premium percentage of rate card also range will be 0% to 300% *@

    <!--<SfNumericTextBox Placeholder="Premium Percentage" FloatLabelType="FloatLabelType.Always"
                              @bind-Value="rateCard.PremiumPercentage"
                              Min="0" Max="300" Decimals="0" ShowSpinButton="false" Format="###">
            </SfNumericTextBox>

        </div>
    </div>-->
    <div class="row">
        <div class="col">
            <SfButton CssClass="e-primary" type="submit">Save</SfButton>
        </div>
    </div>
</EditForm>


<style>
    .e-multiselect.e-input-group.e-control-wrapper.e-input-focus::before, .e-multiselect.e-input-group.e-control-wrapper.e-input-focus::after {
        background: #c000ff;
    }
</style>

@code {

    [SupplyParameterFromForm] public RateCardDto rateCard { get; set; } = new();
    public List<SubPublicationDto> SubPublicationList { get; set; } = new();
    public List<EditionDto> EditionList { get; set; } = new();
    public List<EditionDto> CitiesList { get; set; } = new();
    public List<DayDto> DaysList { get; set; } = new();
    public List<AdTypeDto> AdTypeList { get; set; } = new();
    public List<IndustryDto> IndustryList { get; set; } = new();
    public List<ClientCategoryDto> GovCategoryList { get; set; } = new();
    public List<AdTypeCategoryDto> AllGovAdTypesList { get; set; } = new();
    public List<ClientDto> ClientList { get; set; } = new();
    public List<CategoryDto> CategoryList { get; set; } = new();
    public List<ColorDto> ColorList { get; set; } = new();
    public List<string?> ColorKeys { get; set; } = new();
    private SfToast? _toastObj;
    [Parameter] public int? RateCardId { get; set; }
    private bool _disposed = false;

    private async Task SaveRateCard()
    {
        if (_disposed) return;

        var op = await service.SaveRateCard(rateCard, "jawaid");

        if (_disposed) return;

        if (op != "OK")
        {
            var tm = new ToastModel
            {
                Content = op,
                Title = "Error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000,
                CssClass = "e-toast-danger"
            };
            if (_toastObj != null && !_disposed)
                await _toastObj.ShowAsync(tm);
        }
        else
        {
            var tm = new ToastModel
            {
                Content = "Rate card saved successfully",
                Title = "Success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 3000,
                CssClass = "e-toast-success"
            };
            if (_toastObj != null && !_disposed)
                await _toastObj.ShowAsync(tm);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        if (_disposed) return;

        await base.OnInitializedAsync();

        if (_disposed) return;

        SubPublicationList = await service.GetAllSubPublications();
        EditionList = await service.GetAllEditions();
        AdTypeList = await service.GetAllAdTypes();
        IndustryList = await service.GetAllIndustries();
        ClientList = await service.GetAllClients();
        CategoryList = await service.GetAllCategories();
        ColorList = await service.GetAllColors();
        CitiesList = await service.GetAllCities();
        ColorKeys = ColorList.Select(m => m.Title).ToList();
        GovCategoryList = await service.GetAllGovCategories();
        AllGovAdTypesList = await service.GetAllGovAdType();
        // fill days list
        DaysList =
        [
            new DayDto { Id = 1, Name = "Sunday" },
        new DayDto { Id = 2, Name = "Monday" },
        new DayDto { Id = 3, Name = "Tuesday" },
        new DayDto { Id = 4, Name = "Wednesday" },
        new DayDto { Id = 5, Name = "Thursday" },
        new DayDto { Id = 6, Name = "Friday" },
        new DayDto { Id = 7, Name = "Saturday" }
        ];
    }

    protected override async Task OnParametersSetAsync()
    {
        if (_disposed) return;

        await base.OnParametersSetAsync();

        if (_disposed) return;

        if (RateCardId == null)
        {
            rateCard = new RateCardDto
            {
                Rates = await service.GetRateCardDetail(),
                DateFrom = new DateTime(DateTime.Today.Year, 1, 1),
                DateTo = new DateTime(DateTime.Today.Year, 1, 1).AddYears(1).AddDays(-1),
                Priority = 1,
                IsActive = true
            };
        }
        else
        {
            try
            {
                rateCard = await service.OpenRateCard(RateCardId ?? 0);
                if (rateCard != null && !_disposed)
                {
                    rateCard.Rates = await service.GetRateCardDetail(RateCardId ?? 0);
                }
            }
            catch (Exception ex)
            {
                if (_toastObj != null && !_disposed)
                {
                    var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, ShowProgressBar = true, Timeout = 5000 };
                    await _toastObj.ShowAsync(tm);
                }
            }
        }
    }

    private decimal fixedRate = 0;
    private decimal fixAllColor = 0;
    private decimal fixSpotColor = 0;
    private decimal fixColor = 0;

    private void FillFixRate()
    {
        foreach (var v in rateCard.Rates)
        {            
            // Black and White - only page premium applies
            v.BlackNWhite = fixedRate + (fixedRate * (v.PremiumPercentage / 100));
            
            // Color - add both color premium and page premium separately
            // v.Colour = fixedRate + (fixedRate * (fixColor / 100)) + fixedRate + (fixedRate * (v.PremiumPercentage / 100));
            v.Colour = fixedRate + (fixedRate * fixColor / 100) + fixedRate * (v.PremiumPercentage / 100);
            
            // Spot Color - add both spot color premium and page premium separately
            //v.SpotColour = fixedRate + (fixedRate * (fixSpotColor / 100)) + fixedRate + (fixedRate * (v.PremiumPercentage / 100));
            v.SpotColour = fixedRate + (fixedRate * fixSpotColor / 100) + fixedRate * (v.PremiumPercentage / 100);
            
            // All Color - add both all color premium and page premium separately
            //v.AllColour = fixedRate + (fixedRate * (fixAllColor / 100)) + fixedRate + (fixedRate * (v.PremiumPercentage / 100));
            v.AllColour = fixedRate + (fixedRate * fixAllColor / 100) + fixedRate * (v.PremiumPercentage / 100);
        }
    }

    public async ValueTask DisposeAsync()
    {
        _disposed = true;

        // Give a small delay to allow any pending toast operations to complete
        await Task.Delay(100);

        // Dispose the toast component if it exists
        if (_toastObj is IAsyncDisposable asyncDisposable)
        {
            await asyncDisposable.DisposeAsync();
        }
        else if (_toastObj is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }

}
