﻿namespace JangAaina.DTO;
#nullable disable
public class SpotDto
{
    public Guid Id { get; set; }
    public DateOnly? Date { get; set; }
    public string City { get; set; }
    public string Publication { get; set; }
    public string SubPublication { get; set; }
    public string Industry { get; set; }
    public string Category { get; set; }
    public string Variant { get; set; }
    public string Company { get; set; }
    public string PageType { get; set; }
    public string AdType { get; set; }
    public string Colour { get; set; }
    public string Edition { get; set; }
    public int? Size1 { get; set; }
    public int? Size2 { get; set; }
    public double Space => (Size1 ?? 0) * (Size2 ?? 0);
    public decimal? Rate { get; set; }
    public double? Amount { get; set; }
    public double? Cost { get; set; }
}