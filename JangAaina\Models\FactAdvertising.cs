﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class FactAdvertising
{
    public Guid FactAdvertisingId { get; set; }

    public DateOnly? DateKey { get; set; }

    public string? DayName { get; set; }

    public DateOnly? MonthKey { get; set; }

    public DateOnly? YearKey { get; set; }

    public int? CountryId { get; set; }

    public int? CityId { get; set; }

    public int? PublicationId { get; set; }

    public int? SubPublicationId { get; set; }

    public int? LanguageId { get; set; }

    public int? IndustryId { get; set; }

    public int? CategoryId { get; set; }

    public int? BrandId { get; set; }

    public int? CompanyId { get; set; }

    public int? PageTypeId { get; set; }

    public int? AdTypeId { get; set; }

    public int? ColourId { get; set; }

    public int? MediaAgencyId { get; set; }

    public int? PublishTypeId { get; set; }

    public int? FrequencyId { get; set; }

    public int? GovCategoryId { get; set; }

    public int? GovAdTypeId { get; set; }

    public string? Variant { get; set; }

    public string? Caption { get; set; }

    public int? PageNumber { get; set; }

    public int? Size1 { get; set; }

    public int? Size2 { get; set; }

    public double? TotalSpace { get; set; }

    public double? AdCost { get; set; }

    public decimal Rate { get; set; }

    public int? RateCardId { get; set; }

    public decimal? Amount { get; set; }

    public decimal? AmountAfterDiscount { get; set; }

    public int? EditionId { get; set; }

    public int? EditionCityCount { get; set; }

    public int? FocrateCardId { get; set; }

    public double DiscountPer { get; set; }

    public virtual DimAdType? AdType { get; set; }

    public virtual DimBrand? Brand { get; set; }

    public virtual DimCategory? Category { get; set; }

    public virtual DimCity? City { get; set; }

    public virtual DimColour? Colour { get; set; }

    public virtual DimCompany? Company { get; set; }

    public virtual DimCountry? Country { get; set; }

    public virtual FocrateCard? FocrateCard { get; set; }

    public virtual DimFrequency? Frequency { get; set; }

    public virtual DimGovAdType? GovAdType { get; set; }

    public virtual DimGovCategory? GovCategory { get; set; }

    public virtual DimIndustry? Industry { get; set; }

    public virtual DimLanguage? Language { get; set; }

    public virtual DimMediaAgency? MediaAgency { get; set; }

    public virtual DimPageType? PageType { get; set; }

    public virtual DimPublication? Publication { get; set; }

    public virtual DimPublishType? PublishType { get; set; }

    public virtual DimSubPublication? SubPublication { get; set; }
}