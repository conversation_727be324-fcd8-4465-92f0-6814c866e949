using JangAaina.Data;
using JangAaina.DTO;
using JangAaina.Models;

namespace JangAaina.Services;

public class FOCRateCardService(IApplicationDbContextFactory dbContextFactory)
{
    public Task<List<FOCRateCardDto>> GetAllFOCRateCards(DateTime filterDate, int? editionId)
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.FOCRateCards
                 orderby a.IsActive descending, a.Title
                 where a.DateFrom <= filterDate
                       && filterDate <= a.DateTo
                       && (editionId == null || a.FOCRateCardEditions.Any(m => m.EditionId == editionId))
                 select new FOCRateCardDto
                 {
                     Id = a.Id,
                     Title = a.Title,
                     DateFrom = a.DateFrom,
                     DateTo = a.DateTo,
                     IsActive = a.IsActive,
                     Priority = a.Priority,
                     AllEditions = a.AllEditions,
                     AllGovCategories = a.AllGovCategories,
                     AllGovAdType = a.AllGovAdType,
                     AllClients = a.AllClients,
                     AllIndustries = a.AllIndustries,
                     AllCities = a.AllCities,
                     DiscountPer = a.DiscountPer,
                     EditionStr = a.AllEditions
                         ? "All Editions"
                         : string.Join(", ", a.FOCRateCardEditions.Select(k => k.Edition.EditionName).ToList()),
                     GovCategoryStr = a.AllGovCategories
                         ? "All Categories"
                         : string.Join(", ", a.FOCRateCardGovCategories.Select(k => k.GovCategory.GovCategoryName).ToList()),
                     GovAdTypeStr = a.AllGovAdType
                         ? "All Ad Types"
                         : string.Join(", ", a.FOCRateCardGovAdTypes.Select(k => k.GovAdType.GovAdTypeName).ToList()),
                     ClientStr = a.AllClients
                         ? "All Clients"
                         : string.Join(", ", a.FOCRateCardClients.Select(k => k.Client.CompanyName).ToList()),
                     IndustryStr = a.AllIndustries
                         ? "All Industries"
                         : string.Join(", ", a.FOCRateCardIndustries.Select(k => k.Industry.IndustryName).ToList())
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<EditionDto>> GetAllEditions()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.DimEditions
                 orderby a.EditionName
                 select new EditionDto
                 {
                     Id = a.EditionId,
                     Title = a.EditionName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<ClientCategoryDto>> GetAllGovCategories()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.DimGovCategories
                 orderby a.GovCategoryName
                 select new ClientCategoryDto
                 {
                     Id = a.GovCategoryId,
                     Title = a.GovCategoryName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<AdTypeCategoryDto>> GetAllGovAdType()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.DimGovAdTypes
                 orderby a.GovAdTypeName
                 select new AdTypeCategoryDto
                 {
                     Id = a.GovAdTypeId,
                     Title = a.GovAdTypeName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<ClientDto>> GetAllClients()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.DimCompanies
                 orderby a.CompanyName
                 select new ClientDto
                 {
                     Id = a.CompanyId,
                     Title = a.CompanyName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<IndustryDto>> GetAllIndustries()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.DimIndustries
                 orderby a.IndustryName
                 select new IndustryDto
                 {
                     Id = a.IndustryId,
                     Title = a.IndustryName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> SaveFOCRateCard(FOCRateCardDto rc, string user)
    {
        using var dc = dbContextFactory.CreateDbContext();
        var tran = dc.Database.BeginTransaction();
        try
        {
            var cardId = rc.Id;
            if (rc.Id == 0)
            {
                var card = new FOCRateCard
                {
                    Title = rc.Title,
                    Id = 0,
                    IsActive = rc.IsActive,
                    AllEditions = rc.AllEditions,
                    AllGovCategories = rc.AllGovCategories,
                    AllGovAdType = rc.AllGovAdType,
                    AllClients = rc.AllClients,
                    AllIndustries = rc.AllIndustries,
                    DiscountPer = rc.DiscountPer,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    DateFrom = rc.DateFrom,
                    DateTo = rc.DateTo,
                    Priority = rc.Priority ?? 0,
                    ModifiedBy = user,
                    ModifiedDate = DateTime.Now,
                    AllCities = rc.AllCities
                };
                dc.FOCRateCards.Add(card);
                dc.SaveChanges();
                cardId = card.Id;
            }
            else
            {
                var card = (from a in dc.FOCRateCards
                            where a.Id == rc.Id
                            select a).FirstOrDefault();
                if (card == null) return Task.FromResult("Record not found");

                card.Title = rc.Title;
                card.IsActive = rc.IsActive;
                card.AllEditions = rc.AllEditions;
                card.AllGovCategories = rc.AllGovCategories;
                card.AllGovAdType = rc.AllGovAdType;
                card.AllClients = rc.AllClients;
                card.AllIndustries = rc.AllIndustries;
                card.DiscountPer = rc.DiscountPer;
                card.ModifiedBy = user;
                card.ModifiedDate = DateTime.Now;
                card.DateFrom = rc.DateFrom;
                card.DateTo = rc.DateTo;
                card.Priority = rc.Priority ?? 0;
                card.AllClients = rc.AllClients;
                card.AllCities = rc.AllCities;
                card.AllEditions = rc.AllEditions;
                card.AllGovAdType = rc.AllGovAdType;
                card.AllIndustries = rc.AllIndustries;
                dc.SaveChanges();
            }

            // Delete existing relations
            var existingEditions = dc.FOCRateCardEditions.Where(x => x.FOCRateCardId == cardId);
            dc.FOCRateCardEditions.RemoveRange(existingEditions);
            dc.SaveChanges();

            var existingGovCategories = dc.FOCRateCardGovCategories.Where(x => x.FOCRateCardId == cardId);
            dc.FOCRateCardGovCategories.RemoveRange(existingGovCategories);
            dc.SaveChanges();

            var existingGovAdTypes = dc.FOCRateCardGovAdTypes.Where(x => x.FOCRateCardId == cardId);
            dc.FOCRateCardGovAdTypes.RemoveRange(existingGovAdTypes);
            dc.SaveChanges();

            var existingClients = dc.FOCRateCardClients.Where(x => x.FOCRateCardId == cardId);
            dc.FOCRateCardClients.RemoveRange(existingClients);
            dc.SaveChanges();

            var existingIndustries = dc.FOCRateCardIndustries.Where(x => x.FOCRateCardId == cardId);
            dc.FOCRateCardIndustries.RemoveRange(existingIndustries);
            dc.SaveChanges();

            var existingSubPublications = dc.FOCRateCardSubPublications.Where(x => x.FOCRateCardId == cardId).ToList();
            dc.FOCRateCardSubPublications.RemoveRange(existingSubPublications);
            dc.SaveChanges();

            var existingCities = dc.FOCRateCardCities.Where(x => x.FOCRateCardId == cardId).ToList();
            dc.FOCRateCardCities.RemoveRange(existingCities);

            dc.SaveChanges();

            // Add new relations
            if (!rc.AllEditions && rc.SelectedEditionList?.Any() == true)
            {
                foreach (var edition in rc.SelectedEditionList)
                    dc.FOCRateCardEditions.Add(new FOCRateCardEdition
                    {
                        FOCRateCardId = cardId,
                        EditionId = edition.Id,
                        CreatedDate = DateTime.Now
                    });
                dc.SaveChanges();
            }

            if (!rc.AllGovCategories && rc.SelectedGovCategoryList?.Any() == true)
            {
                foreach (var category in rc.SelectedGovCategoryList)
                    dc.FOCRateCardGovCategories.Add(new FOCRateCardGovCategory
                    {
                        FOCRateCardId = cardId,
                        GovCategoryId = category.Id,
                        CreateDate = DateTime.Now
                    });
                dc.SaveChanges();
            }

            if (!rc.AllGovAdType && rc.SelectedGovAdTypes?.Any() == true)
            {
                foreach (var adType in rc.SelectedGovAdTypes)
                    dc.FOCRateCardGovAdTypes.Add(new FOCRateCardGovAdType
                    {
                        FOCRateCardId = cardId,
                        GovAdTypeId = adType.Id,
                        CreateDate = DateTime.Now
                    });
                dc.SaveChanges();
            }

            if (!rc.AllClients && rc.SelectedClientList?.Any() == true)
            {
                foreach (var client in rc.SelectedClientList)
                    dc.FOCRateCardClients.Add(new FOCRateCardClient
                    {
                        FOCRateCardId = cardId,
                        ClientId = client.Id,
                        CreatedDate = DateTime.Now
                    });
                dc.SaveChanges();
            }

            if (!rc.AllIndustries && rc.SelectedIndustryList?.Any() == true)
            {
                foreach (var industry in rc.SelectedIndustryList)
                    dc.FOCRateCardIndustries.Add(new FOCRateCardIndustry
                    {
                        FOCRateCardId = cardId,
                        IndustryId = industry.Id,
                        CreatedDate = DateTime.Now
                    });
                dc.SaveChanges();
            }

            if (!rc.AllCities && rc.SelectedCitiesList?.Any() == true)
            {
                foreach (var ct in rc.SelectedCitiesList)
                {
                    dc.FOCRateCardCities.Add(new FOCRateCardCity
                    {
                        CityId = ct.CityId,
                        FOCRateCardId = cardId,
                        CreatedDate = DateTime.Now
                    });

                }
                dc.SaveChanges();

            }

            foreach (var sp in rc.SelectedSubPublications)
                dc.FOCRateCardSubPublications.Add(new FOCRateCardSubPublication
                {
                    SubPublicationId = sp.Id,
                    FOCRateCardId = cardId,
                    CreatedDate = DateTime.Now
                });

            dc.SaveChanges();
            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            tran.Rollback();
            return Task.FromResult(ex.Message);
        }
    }

    public Task<FOCRateCardDto> OpenFOCRateCard(int id)
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.FOCRateCards
                 where a.Id == id
                 select new FOCRateCardDto
                 {
                     Id = a.Id,
                     Title = a.Title,
                     DateFrom = a.DateFrom,
                     DateTo = a.DateTo,
                     Priority = a.Priority,
                     IsActive = a.IsActive,
                     AllEditions = a.AllEditions,
                     AllGovCategories = a.AllGovCategories,
                     AllGovAdType = a.AllGovAdType,
                     AllClients = a.AllClients,
                     AllIndustries = a.AllIndustries,
                     DiscountPer = a.DiscountPer,
                     AllCities = a.AllCities,
                     
                 }).First();

        q.SelectedEditionList = (from a in dc.FOCRateCardEditions
                                 where a.FOCRateCardId == id
                                 select new EditionDto
                                 {
                                     Id = a.EditionId,
                                     Title = a.Edition.EditionName
                                 }).ToList();

        q.SelectedGovCategoryList = (from a in dc.FOCRateCardGovCategories
                                     where a.FOCRateCardId == id
                                     select new ClientCategoryDto
                                     {
                                         Id = a.GovCategoryId,
                                         Title = a.GovCategory.GovCategoryName
                                     }).ToList();

        q.SelectedGovAdTypes = (from a in dc.FOCRateCardGovAdTypes
                                where a.FOCRateCardId == id
                                select new AdTypeCategoryDto
                                {
                                    Id = a.GovAdTypeId,
                                    Title = a.GovAdType.GovAdTypeName
                                }).ToList();

        q.SelectedClientList = (from a in dc.FOCRateCardClients
                                where a.FOCRateCardId == id
                                select new ClientDto
                                {
                                    Id = a.ClientId,
                                    Title = a.Client.CompanyName
                                }).ToList();

        q.SelectedIndustryList = (from a in dc.FOCRateCardIndustries
                                  where a.FOCRateCardId == id
                                  select new IndustryDto
                                  {
                                      Id = a.IndustryId,
                                      Title = a.Industry.IndustryName
                                  }).ToList();

        q.SelectedSubPublications = (from a in dc.FOCRateCardSubPublications
                                     where a.FOCRateCardId == id
                                     select new SubPublicationDto
                                     {
                                         Id = a.SubPublicationId,
                                         Title = a.SubPublication.SubPublicationName
                                     }).ToList();

        q.SelectedCitiesList = (from a in dc.FOCRateCardCities
                                where a.FOCRateCardId == id
                                select new CityDto
                                {
                                    CityId = a.CityId,
                                    CityName = a.City.CityName
                                }).ToList();

        q.SelectedClientList = (from a in dc.FOCRateCardClients
                                where a.FOCRateCardId == id
                                select new ClientDto
                                {
                                    Id = a.ClientId,
                                    Title = a.Client.CompanyName
                                }).ToList();

        return Task.FromResult(q);
    }

    public Task<string> DeleteFOCRateCard(int id)
    {
        using var dc = dbContextFactory.CreateDbContext();
        try
        {
            var card = dc.FOCRateCards.Find(id);
            if (card == null) return Task.FromResult("Record not found");

            // Remove related records
            var editions = dc.FOCRateCardEditions.Where(x => x.FOCRateCardId == id);
            dc.FOCRateCardEditions.RemoveRange(editions);

            var govCategories = dc.FOCRateCardGovCategories.Where(x => x.FOCRateCardId == id);
            dc.FOCRateCardGovCategories.RemoveRange(govCategories);

            var govAdTypes = dc.FOCRateCardGovAdTypes.Where(x => x.FOCRateCardId == id);
            dc.FOCRateCardGovAdTypes.RemoveRange(govAdTypes);

            var clients = dc.FOCRateCardClients.Where(x => x.FOCRateCardId == id);
            dc.FOCRateCardClients.RemoveRange(clients);

            var industries = dc.FOCRateCardIndustries.Where(x => x.FOCRateCardId == id);
            dc.FOCRateCardIndustries.RemoveRange(industries);

            var subPublications = dc.FOCRateCardSubPublications.Where(x => x.FOCRateCardId == id);
            dc.FOCRateCardSubPublications.RemoveRange(subPublications);

            dc.FOCRateCards.Remove(card);
            dc.SaveChanges();

            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public Task<List<SubPublicationDto>> GetAllSubPublications()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.DimSubPublications
                 orderby a.SubPublicationName
                 select new SubPublicationDto
                 {
                     Id = a.SubPublicationId,
                     Title = a.SubPublicationName
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<CityDto>> GetAllCities()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var q = (from a in dc.DimCities
                 orderby a.CityName
                 select new CityDto
                 {
                     CityId = a.CityId,
                     CityName = a.CityName
                 }).ToList();
        return Task.FromResult(q);
    }
}