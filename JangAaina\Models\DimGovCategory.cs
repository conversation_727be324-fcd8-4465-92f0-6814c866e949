﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace JangAaina.Models;

public partial class DimGovCategory
{
    public int GovCategoryId { get; set; }

    public string GovCategoryName { get; set; } = null!;

    public virtual ICollection<FactAdvertising> FactAdvertisings { get; set; } = new List<FactAdvertising>();

    public virtual ICollection<FocrateCardCategory> FocrateCardCategories { get; set; } = new List<FocrateCardCategory>();

    public virtual ICollection<FocrateCardGovCategory> FocrateCardGovCategories { get; set; } = new List<FocrateCardGovCategory>();

    public virtual ICollection<RateCardGovCategory> RateCardGovCategories { get; set; } = new List<RateCardGovCategory>();
}